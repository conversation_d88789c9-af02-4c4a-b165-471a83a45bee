<route lang="json">
{
  "style": {
    "navigationBarTitleText": "账户中心"
  }
}
</route>

<template>
  <view class="profile-container">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <!-- 头像区域 -->
      <view class="avatar-section" @click="handleAvatarClick">
        <image 
          :src="userStore.userInfo.headerImg || '/static/images/default-avatar.png'" 
          class="avatar"
          mode="aspectFill"
        />
        <view class="avatar-edit-icon">
          <wd-icon name="camera" size="24rpx" color="#fff" />
        </view>
      </view>
      
      <!-- 用户基本信息 -->
      <view class="user-info">
        <text class="username">{{ userStore.userInfo.nickName || '未设置昵称' }}</text>
        <text class="user-id">ID: {{ userStore.userInfo.userName }}</text>
      </view>
    </view>

    <!-- 个人资料列表 -->
    <view class="profile-list">
      <view class="section-title">个人资料</view>
      
      <!-- 昵称 -->
      <view class="list-item" @click="editFieldHandler('nickName')">
        <view class="item-left">
          <wd-icon name="user" size="32rpx" color="#667eea" />
          <text class="item-label">昵称</text>
        </view>
        <view class="item-right">
          <text class="item-value">{{ userStore.userInfo.nickName || '未设置' }}</text>
          <wd-icon name="arrow-right" size="24rpx" color="#c0c4cc" />
        </view>
      </view>

      <!-- 手机号 -->
      <view class="list-item">
        <view class="item-left">
          <wd-icon name="phone" size="32rpx" color="#667eea" />
          <text class="item-label">手机号</text>
        </view>
        <view class="item-right">
          <text class="item-value">{{ userStore.userInfo.phone || '未绑定' }}</text>
        </view>
      </view>

      <!-- 企业名称 -->
      <view class="list-item" @click="editFieldHandler('companyName')">
        <view class="item-left">
          <wd-icon name="home" size="32rpx" color="#667eea" />
          <text class="item-label">企业名称</text>
        </view>
        <view class="item-right">
          <text class="item-value">{{ userStore.userInfo.companyName || '未设置' }}</text>
          <wd-icon name="arrow-right" size="24rpx" color="#c0c4cc" />
        </view>
      </view>
    </view>

    <!-- 账户安全列表 -->
    <view class="profile-list">
      <view class="section-title">账户安全</view>
      
      <!-- 修改密码 -->
      <view class="list-item" @click="goToChangePassword">
        <view class="item-left">
          <wd-icon name="lock" size="32rpx" color="#667eea" />
          <text class="item-label">修改密码</text>
        </view>
        <view class="item-right">
          <wd-icon name="arrow-right" size="24rpx" color="#c0c4cc" />
        </view>
      </view>
    </view>

    <!-- 其他操作列表 -->
    <view class="profile-list">
      <view class="section-title">其他操作</view>

      <!-- 退出登录 -->
      <view class="list-item" @click="handleLogout">
        <view class="item-left">
          <wd-icon name="logout" size="32rpx" color="#f56c6c" />
          <text class="item-label logout-text">退出登录</text>
        </view>
        <view class="item-right">
          <wd-icon name="arrow-right" size="24rpx" color="#c0c4cc" />
        </view>
      </view>
    </view>

    <!-- 编辑弹窗 -->
    <wd-popup 
      v-model="showEditPopup" 
      position="bottom" 
      :safe-area-inset-bottom="true"
    >
      <view class="edit-popup">
        <view class="popup-header">
          <text class="popup-title">编辑{{ editFieldLabel }}</text>
          <wd-icon name="close" size="32rpx" @click="closeEditPopup" />
        </view>
        <view class="popup-content">
          <wd-input
            v-model="editValue"
            :placeholder="`请输入${editFieldLabel}`"
            clearable
            :maxlength="50"
          />
        </view>
        <view class="popup-actions">
          <wd-button 
            type="default" 
            size="large" 
            custom-class="cancel-btn"
            @click="closeEditPopup"
          >
            取消
          </wd-button>
          <wd-button 
            type="primary" 
            size="large" 
            custom-class="save-btn"
            @click="saveEdit"
            :loading="saving"
          >
            保存
          </wd-button>
        </view>
      </view>
    </wd-popup>

    <!-- 头像裁剪弹窗 -->
    <wd-popup
      v-model="showAvatarCropper"
      position="bottom"
      :safe-area-inset-bottom="true"
    >
      <view class="avatar-cropper-popup">
        <view class="popup-header">
          <text class="popup-title">裁剪头像</text>
          <wd-icon name="close" size="32rpx" @click="closeAvatarCropper" />
        </view>

        <!-- 图片预览区域 -->
        <view class="image-preview">
          <image
            v-if="selectedImagePath"
            :src="selectedImagePath"
            class="preview-image"
            mode="aspectFit"
          />
        </view>

        <!-- 裁剪提示 -->
        <view class="crop-tips">
          <text>建议使用正方形头像，系统会自动裁剪为圆形显示</text>
        </view>

        <view class="popup-actions">
          <wd-button
            type="default"
            size="large"
            custom-class="cancel-btn"
            @click="closeAvatarCropper"
          >
            取消
          </wd-button>
          <wd-button
            type="primary"
            size="large"
            custom-class="upload-btn"
            @click="uploadAvatar"
            :loading="uploading"
          >
            {{ uploading ? '上传中...' : '确认上传' }}
          </wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { toast } from '@/utils/toast'

const userStore = useUserStore()

// 编辑相关状态
const showEditPopup = ref(false)
const editField = ref('')
const editFieldLabel = ref('')
const editValue = ref('')
const saving = ref(false)

// 头像上传相关状态
const showAvatarCropper = ref(false)
const selectedImagePath = ref('')
const uploading = ref(false)

// 文件上传配置
const uploadUrl = `${import.meta.env.VITE_SERVER_BASEURL}/fileUploadAndDownload/upload`

// 页面加载时获取用户信息
onMounted(async () => {
  try {
    await userStore.getUserProfile()
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
})

// 处理头像点击
const handleAvatarClick = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      selectedImagePath.value = res.tempFilePaths[0]
      showAvatarCropper.value = true
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
      toast.error('选择图片失败')
    }
  })
}

// 关闭头像裁剪弹窗
const closeAvatarCropper = () => {
  showAvatarCropper.value = false
  selectedImagePath.value = ''
}

// 上传头像
const uploadAvatar = async () => {
  if (!selectedImagePath.value) {
    toast.error('请先选择图片')
    return
  }

  uploading.value = true
  try {
    // 使用uni.uploadFile上传图片
    const uploadResult = await new Promise<any>((resolve, reject) => {
      uni.uploadFile({
        url: uploadUrl,
        filePath: selectedImagePath.value,
        name: 'file',
        header: {
          'x-token': userStore.token
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data)
            if (data.code === 0) {
              resolve(data.data.file)
            } else {
              reject(new Error(data.msg || '上传失败'))
            }
          } catch (error) {
            reject(new Error('响应解析失败'))
          }
        },
        fail: (error) => {
          reject(error)
        }
      })
    })

    // 更新用户头像
    await userStore.updateUserProfile({
      headerImg: uploadResult.url
    })

    closeAvatarCropper()
    toast.success('头像更新成功')
  } catch (error: any) {
    console.error('头像上传失败:', error)
    toast.error(error.message || '头像上传失败')
  } finally {
    uploading.value = false
  }
}

// 编辑字段
const editFieldHandler = (field: string) => {
  const fieldMap: Record<string, string> = {
    nickName: '昵称',
    companyName: '企业名称'
  }

  editField.value = field
  editFieldLabel.value = fieldMap[field]
  editValue.value = userStore.userInfo[field as keyof typeof userStore.userInfo] as string || ''
  showEditPopup.value = true
}

// 关闭编辑弹窗
const closeEditPopup = () => {
  showEditPopup.value = false
  editField.value = ''
  editFieldLabel.value = ''
  editValue.value = ''
}

// 保存编辑
const saveEdit = async () => {
  if (!editValue.value.trim()) {
    toast.error(`${editFieldLabel.value}不能为空`)
    return
  }

  saving.value = true
  try {
    const updateData = {
      [editField.value]: editValue.value.trim()
    }
    
    await userStore.updateUserProfile(updateData)
    closeEditPopup()
    toast.success('保存成功')
  } catch (error) {
    console.error('保存失败:', error)
    toast.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

// 跳转到修改密码页面
const goToChangePassword = () => {
  uni.navigateTo({
    url: '/pages/profile/change-password'
  })
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        await userStore.logout()
        uni.reLaunch({
          url: '/pages/login/index'
        })
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 40rpx 32rpx;
}

.user-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(10rpx);
}

.avatar-section {
  position: relative;
  margin-right: 40rpx;
  
  .avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 60rpx;
    border: 4rpx solid #fff;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  }
  
  .avatar-edit-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 40rpx;
    height: 40rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2rpx solid #fff;
  }
}

.user-info {
  flex: 1;
  
  .username {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
  }
  
  .user-id {
    font-size: 26rpx;
    color: #909399;
  }
}

.profile-list {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(10rpx);
}

.section-title {
  padding: 32rpx 40rpx 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

.list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #f8f8f8;
  transition: background-color 0.3s ease;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background-color: #f8f9fa;
  }
}

.item-left {
  display: flex;
  align-items: center;
  
  .item-label {
    margin-left: 24rpx;
    font-size: 30rpx;
    color: #333;
  }
}

.item-right {
  display: flex;
  align-items: center;
  
  .item-value {
    font-size: 28rpx;
    color: #606266;
    margin-right: 16rpx;
  }
}

.logout-text {
  color: #f56c6c !important;
}

.edit-popup {
  padding: 40rpx;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
  
  .popup-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
}

.popup-content {
  margin-bottom: 60rpx;
}

.popup-actions {
  display: flex;
  gap: 24rpx;
  
  .cancel-btn,
  .save-btn {
    flex: 1;
    height: 88rpx !important;
    border-radius: 44rpx !important;
    font-size: 30rpx !important;
  }
}

:deep(.save-btn) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
}

.avatar-cropper-popup {
  padding: 40rpx;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
}

.image-preview {
  margin: 40rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  overflow: hidden;

  .preview-image {
    max-width: 100%;
    max-height: 400rpx;
    border-radius: 16rpx;
  }
}

.crop-tips {
  text-align: center;
  margin-bottom: 40rpx;

  text {
    font-size: 24rpx;
    color: #909399;
    line-height: 1.5;
  }
}

:deep(.upload-btn) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
}
</style>
