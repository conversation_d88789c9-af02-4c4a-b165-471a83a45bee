<route lang="json">
{
  "style": {
    "navigationBarTitleText": "账户中心"
  }
}
</route>

<template>
  <view class="profile-container">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <!-- 头像区域 -->
      <view class="avatar-section" @click="handleAvatarClick">
        <image 
          :src="userStore.userInfo.headerImg || '/static/images/default-avatar.png'" 
          class="avatar"
          mode="aspectFill"
        />
        <view class="avatar-edit-icon">
          <wd-icon name="camera" size="24rpx" color="#fff" />
        </view>
      </view>
      
      <!-- 用户基本信息 -->
      <view class="user-info">
        <text class="username">{{ userStore.userInfo.nickName || '未设置昵称' }}</text>
        <text class="user-id">ID: {{ userStore.userInfo.userName }}</text>
      </view>
    </view>

    <!-- 个人资料列表 -->
    <view class="profile-list">
      <view class="section-title">个人资料</view>
      
      <!-- 昵称 -->
      <view class="list-item" @click="editFieldHandler('nickName')">
        <view class="item-left">
          <wd-icon name="user" size="32rpx" color="#667eea" />
          <text class="item-label">昵称</text>
        </view>
        <view class="item-right">
          <text class="item-value">{{ userStore.userInfo.nickName || '未设置' }}</text>
          <wd-icon name="arrow-right" size="24rpx" color="#c0c4cc" />
        </view>
      </view>

      <!-- 手机号 -->
      <view class="list-item">
        <view class="item-left">
          <wd-icon name="phone" size="32rpx" color="#667eea" />
          <text class="item-label">手机号</text>
        </view>
        <view class="item-right">
          <text class="item-value">{{ userStore.userInfo.phone || '未绑定' }}</text>
        </view>
      </view>

      <!-- 企业名称 -->
      <view class="list-item" @click="editFieldHandler('companyName')">
        <view class="item-left">
          <wd-icon name="home" size="32rpx" color="#667eea" />
          <text class="item-label">企业名称</text>
        </view>
        <view class="item-right">
          <text class="item-value">{{ userStore.userInfo.companyName || '未设置' }}</text>
          <wd-icon name="arrow-right" size="24rpx" color="#c0c4cc" />
        </view>
      </view>
    </view>

    <!-- 账户安全列表 -->
    <view class="profile-list">
      <view class="section-title">账户安全</view>
      
      <!-- 修改密码 -->
      <view class="list-item" @click="goToChangePassword">
        <view class="item-left">
          <wd-icon name="lock" size="32rpx" color="#667eea" />
          <text class="item-label">修改密码</text>
        </view>
        <view class="item-right">
          <wd-icon name="arrow-right" size="24rpx" color="#c0c4cc" />
        </view>
      </view>
    </view>

    <!-- 退出登录按钮 -->
    <view class="logout-section">
      <wd-button 
        type="error" 
        size="large" 
        custom-class="logout-btn"
        @click="handleLogout"
      >
        退出登录
      </wd-button>
    </view>

    <!-- 编辑弹窗 -->
    <wd-popup 
      v-model="showEditPopup" 
      position="bottom" 
      :safe-area-inset-bottom="true"
    >
      <view class="edit-popup">
        <view class="popup-header">
          <text class="popup-title">编辑{{ editFieldLabel }}</text>
          <wd-icon name="close" size="32rpx" @click="closeEditPopup" />
        </view>
        <view class="popup-content">
          <wd-input
            v-model="editValue"
            :placeholder="`请输入${editFieldLabel}`"
            clearable
            :maxlength="50"
          />
        </view>
        <view class="popup-actions">
          <wd-button 
            type="default" 
            size="large" 
            custom-class="cancel-btn"
            @click="closeEditPopup"
          >
            取消
          </wd-button>
          <wd-button 
            type="primary" 
            size="large" 
            custom-class="save-btn"
            @click="saveEdit"
            :loading="saving"
          >
            保存
          </wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { toast } from '@/utils/toast'

const userStore = useUserStore()

// 编辑相关状态
const showEditPopup = ref(false)
const editField = ref('')
const editFieldLabel = ref('')
const editValue = ref('')
const saving = ref(false)

// 页面加载时获取用户信息
onMounted(async () => {
  try {
    await userStore.getUserProfile()
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
})

// 处理头像点击
const handleAvatarClick = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      // TODO: 实现图片上传和裁剪功能
      console.log('选择的图片:', res.tempFilePaths[0])
      toast.info('图片上传功能开发中...')
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
      toast.error('选择图片失败')
    }
  })
}

// 编辑字段
const editFieldHandler = (field: string) => {
  const fieldMap: Record<string, string> = {
    nickName: '昵称',
    companyName: '企业名称'
  }

  editField.value = field
  editFieldLabel.value = fieldMap[field]
  editValue.value = userStore.userInfo[field as keyof typeof userStore.userInfo] as string || ''
  showEditPopup.value = true
}

// 关闭编辑弹窗
const closeEditPopup = () => {
  showEditPopup.value = false
  editField.value = ''
  editFieldLabel.value = ''
  editValue.value = ''
}

// 保存编辑
const saveEdit = async () => {
  if (!editValue.value.trim()) {
    toast.error(`${editFieldLabel.value}不能为空`)
    return
  }

  saving.value = true
  try {
    const updateData = {
      [editField.value]: editValue.value.trim()
    }
    
    await userStore.updateUserProfile(updateData)
    closeEditPopup()
    toast.success('保存成功')
  } catch (error) {
    console.error('保存失败:', error)
    toast.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

// 跳转到修改密码页面
const goToChangePassword = () => {
  uni.navigateTo({
    url: '/pages/profile/change-password'
  })
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        await userStore.logout()
        uni.reLaunch({
          url: '/pages/login/index'
        })
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 40rpx 32rpx;
}

.user-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(10rpx);
}

.avatar-section {
  position: relative;
  margin-right: 40rpx;
  
  .avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 60rpx;
    border: 4rpx solid #fff;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  }
  
  .avatar-edit-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 40rpx;
    height: 40rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2rpx solid #fff;
  }
}

.user-info {
  flex: 1;
  
  .username {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
  }
  
  .user-id {
    font-size: 26rpx;
    color: #909399;
  }
}

.profile-list {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(10rpx);
}

.section-title {
  padding: 32rpx 40rpx 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

.list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #f8f8f8;
  transition: background-color 0.3s ease;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background-color: #f8f9fa;
  }
}

.item-left {
  display: flex;
  align-items: center;
  
  .item-label {
    margin-left: 24rpx;
    font-size: 30rpx;
    color: #333;
  }
}

.item-right {
  display: flex;
  align-items: center;
  
  .item-value {
    font-size: 28rpx;
    color: #606266;
    margin-right: 16rpx;
  }
}

.logout-section {
  margin-top: 60rpx;
}

:deep(.logout-btn) {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%) !important;
  border: none !important;
  border-radius: 44rpx !important;
  height: 88rpx !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
}

.edit-popup {
  padding: 40rpx;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
  
  .popup-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
}

.popup-content {
  margin-bottom: 60rpx;
}

.popup-actions {
  display: flex;
  gap: 24rpx;
  
  .cancel-btn,
  .save-btn {
    flex: 1;
    height: 88rpx !important;
    border-radius: 44rpx !important;
    font-size: 30rpx !important;
  }
}

:deep(.save-btn) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
}
</style>
