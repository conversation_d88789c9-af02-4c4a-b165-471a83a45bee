# CLAUDE.md
充分调用 sequential-thinking 工具来思考

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 1. Repository Overview

This is a comprehensive monorepo for a quantitative trading system containing five main projects:

1.  **`admin` 目录**: 负责后端服务 (`server`) 和后台管理界面 (`web`)。
    -   `server`: 基于Go/Gin的后端服务，提供核心业务逻辑、数据接口和第三方服务集成。
    -   `web`: 基于Vue3的后台管理前端界面，供管理员使用。

2.  **`app` 目录**: 负责直接面向用户的跨端应用程序，基于 `uni-app` 开发，可编译为H5、小程序、App等多个平台。

3.  **`hq` 目录**: 量化交易行情引擎，基于Python和vnpy框架开发，负责期货行情数据的接收、处理和转发。

4.  **`order` 目录**: 期货交易客户端，基于Python和vnpy框架开发，提供用户登录认证和交易功能。

5.  **`scripts` 目录**: 项目工具脚本集合，基于Python开发，包含数据库操作、合约信息同步等辅助功能。

## 2. Tech Stack

### 2.1. Backend (`admin/server`)

| 技术 | 名称/版本 | 说明 |
|---|---|---|
| **语言** | Go | 主要开发语言。 |
| **Web框架** | Gin | 高性能Web框架，用于构建RESTful API。 |
| **数据库** | MySQL | 主要业务数据库，使用 `InnoDB` 引擎。 |
| **缓存** | Redis | 用于缓存热门数据、JWT令牌管理、实现多点登录限制等。 |
| **ORM** | GORM | 数据库操作工具，简化数据库交互。 |
| **配置管理** | Viper | 用于管理 `config.yaml` 配置文件。 |
| **日志** | Zap | 高性能日志库，用于记录应用日志。 |
| **API文档** | Swagger | 自动生成并维护API接口文档。 |

### 2.2. Admin Frontend (`admin/web`)

| 技术 | 名称/版本 | 说明 |
|---|---|---|
| **框架** | Vue 3 | 主要前端框架。 |
| **UI库** | Element Plus | 提供丰富的后台UI组件。 |
| **构建工具** | Vite | 提供快速的开发服务器和打包体验。 |
| **状态管理** | Pinia | Vue 3官方推荐的状态管理库。 |

### 2.3. User App (`app`)

| 技术 | 名称/版本 | 说明 |
|---|---|---|
| **框架** | uni-app | 基于 Vue 3 的跨端开发框架。 |
| **开发语言** | TypeScript | 为项目提供类型安全。 |
| **UI样式** | UnoCSS | 原子化CSS框架，实现快速、灵活的样式开发。 |
| **状态管理** | Pinia | 统一的状态管理方案。 |
| **路由** | 约定式路由 | 基于文件的路由系统，由 `vite-plugin-uni-pages` 插件管理。 |
| **包管理器**| pnpm | 高效的包管理工具。 |

## 3. Development Commands

### 3.1. Admin Project (gin-vue-admin)

**Server (Go backend):**
```bash
cd admin/server
go run .                    # Run development server
go generate                 # Install dependencies
swag init                   # Generate swagger docs
go build                    # Build binary
```

**Web (Vue frontend):**
```bash
cd admin/web
npm install                 # Install dependencies
npm run serve               # Development server
npm run build               # Production build
npm run lint                # Lint code
```

**Docker/Production (from admin/):**
```bash
make build                  # Build both frontend and backend
make build-web              # Build frontend only
make build-server           # Build backend only
make doc                    # Generate swagger documentation
make plugin PLUGIN=email   # Package plugin (default: email)
```

### 3.2. App Project (UniApp)

```bash
cd app
pnpm install                # Install dependencies
pnpm dev                    # Run H5 development server
pnpm dev:h5                 # Run H5 (same as dev)
pnpm dev:mp                 # Run WeChat mini-program
pnpm dev:app                # Run native app development
pnpm build                  # Build for production (H5)
pnpm build:h5               # Build H5 for production
pnpm build:mp               # Build WeChat mini-program
pnpm build:app              # Build native app
pnpm lint                   # Lint and fix code
pnpm type-check             # TypeScript type checking
```

## 4. Development Standards and Constraints

### 4.1. General
1.  **Code Style**: 
    -   All frontend code (Vue, TS, JS) must be formatted with `ESLint` and `Prettier` before commit.
    -   Backend Go code follows `gofmt` and `goimports` standards.
2.  **Git Commits**:
    -   Strictly follow `Conventional Commits` specification.
    -   Commit message format: `type(scope): subject`, e.g., `feat(user): add user registration feature`.
    -   Validated by rules in `.commitlintrc.cjs`.
3.  **Dependency Management**:
    -   Use `pnpm` as the package manager for all projects. Do not use `npm` or `yarn`.

### 4.2. Backend (`admin/server`)
1.  **Directory Structure**: Follow the standard `gin-vue-admin` structure.
    -   `api`: API layer for requests/responses.
    -   `service`: Service layer for core business logic.
    -   `model`: Data model layer for database schemas and request/response structs.
    -   `router`: Routing layer for API route definitions.
    -   `config`: Structs corresponding to configuration files.
    -   `global`: Global variables and objects.
    -   `initialize`: Initialization of services and connections.
2.  **API Design**: 
    -   All APIs must be registered in the `router` directory and follow `RESTful` style.
    -   API paths should be lowercase and use hyphens (e.g., `/user-info`).
    -   All APIs should be documented with Swagger annotations in the `docs` directory.
3.  **Database**: 
    -   Schema migrations should be managed by GORM's `AutoMigrate` or separate migration scripts.
    -   Do not use raw SQL strings in code to prevent SQL injection; use GORM.
4.  **Configuration**: All configurations (DB, Redis, JWT) are managed in `config.yaml`. No hardcoding.
5.  **Error Handling**: 
    -   Errors must be returned and propagated up to the `api` layer for standardized JSON response handling.
    -   Use `zap` for detailed error logging.

### 4.3. User App (`app`)
1.  **Cross-Platform Compatibility**: 
    -   Core requirement: All components and pages must function and appear consistently across **H5, WeChat Mini-Program, iOS App, and Android App**.
    -   Use conditional compilation (`#ifdef` / `#endif`) only when necessary for platform differences.
    -   Prefer `uni-app` cross-platform APIs over platform-specific ones.
2.  **Responsive Layout**: 
    -   **Must** use `rpx` as the primary unit for dimensions to ensure adaptability across different screen sizes.
    -   Use `UnoCSS` responsive breakpoints for complex layout adaptation.
    -   Avoid fixed `px` units unless there is a specific, justified reason.
3.  **Componentization**: 
    -   Reusable UI and logic should be encapsulated in components under `src/components`.
    -   Follow Vue 3 `Composition API` (`<script setup>`) style.
4.  **State Management**: Global state (e.g., user info) should be managed with `Pinia` in the `src/store` directory.
5.  **Routing and Pages**: 
    -   Pages are located in `src/pages` (main package) and `src/pages-sub` (sub-packages).
    -   Routing is convention-based (file-based). Page configuration (e.g., navigation bar title) is defined in the `<route>` block of `.vue` files.
6.  **Static Assets**: Static assets like images are stored in `src/static` and referenced with absolute paths `/static/...`.

## 5. Server-side Code Structure
To maintain a clean and maintainable project structure, all server-side business logic must adhere to the following conventions:

- **API Layer:** All business-related API endpoint code must be located in the `server/api/v1/dianjia/` directory.
- **Model Layer:** Database models and struct definitions must be located in the `server/model/dianjia/` directory.
- **Service Layer:** Core business logic processing must be located in the `server/service/dianjia/` directory.
- **Router Layer:** Route registration and middleware configuration must be located in the `server/router/dianjia/` directory.

**Summary:** All new business module code must be categorized by its function (api, model, service, router) and placed into the corresponding `server/*/dianjia/` subdirectory. Any changes or additions outside these specified locations require explicit approval from the project lead (the user).

## 6. Configuration Files

### Admin Project
- `admin/server/config.yaml` - Backend configuration (database, redis, jwt, etc.)
- `admin/web/vite.config.js` - Frontend build configuration
- `admin/Makefile` - Build and deployment scripts

### App Project
- `app/manifest.config.ts` - UniApp manifest configuration
- `app/pages.config.ts` - Page routing configuration
- `app/vite.config.ts` - Vite build configuration
- `app/uno.config.ts` - UnoCSS configuration

## 7. Testing and Quality

### Admin Project
- Server: Run tests with `go test` (no specific test framework mentioned)
- Web: No specific test configuration found

### App Project
- Type checking: `pnpm type-check`
- Linting: `pnpm lint` (ESLint with @antfu/eslint-config)
- Pre-commit hooks: Husky + lint-staged for code quality

## 8. Environment and Deployment
-   **Development**: Use `pnpm dev:*` commands to start the development server for the target platform.
-   **Production Build**: Use `pnpm build:*` commands for production builds.
-   **CI/CD**: The project is configured with GitHub Actions for automated testing, building, and deployment. See the `.github/workflows` directory for details.

## 9. AI-Assisted Development Workflow

### 9.1. Core Principles
1.  **Developer in Charge**: AI is an assistant; the developer is ultimately responsible for the code. **Do not copy-paste AI-generated code without thorough review and testing.**
2.  **Follow Standards**: All AI-generated code must adhere to this project's established code style, architecture, and development standards.
3.  **Security First**: Never provide sensitive information to AI tools, such as private keys, API credentials, or user data.
4.  **Incremental Steps**: Break down complex tasks into smaller pieces when prompting the AI to get more accurate results.

### 9.2. Recommended Use Cases
-   **Code Generation**: Generate function bodies, boilerplate code (CRUD, forms), etc.
-   **Code Explanation & Documentation**: Explain unfamiliar code or generate comments and docs.
-   **Refactoring & Optimization**: Suggest improvements for readability, performance, or design patterns.
-   **Test Generation**: Create initial test cases for functions or components.
-   **Bug Fixes**: Analyze error messages and suggest potential fixes.

### 9.3. Workflow
1.  **Define**: Clearly define your goal before prompting.
2.  **Contextualize**: Provide necessary context, such as existing code snippets (model definitions, related functions) and project standards ("Use GORM for this query," "Generate code for Vue 3 `<script setup>`").
3.  **Generate & Review**: This is a mandatory step. Review the generated code for correctness, compliance with standards, security, and performance.
4.  **Integrate & Test**: Integrate the reviewed code and test it thoroughly (manually and with automated tests).
5.  **Iterate**: If the initial result isn't perfect, refine your prompt or modify the output.

## 10. Best Practice Development Workflow

This section combines the PRD, this document, and other standards into a recommended end-to-end development workflow.

### 10.1. Requirement & Task Breakdown
1.  **Requirement Analysis**: Use the PRD as the single source of truth. Review all functional and non-functional requirements with product, dev, and QA teams.
2.  **Task Breakdown**: Break down requirements into deliverable tasks (Stories/Issues) with clear goals, owners, and acceptance criteria.

### 10.2. Git & Branching
1.  **Branching Model**: Use a simplified Git Flow:
    -   `main`: Stable, deployable production code.
    -   `develop`: Integration branch for all new features and fixes.
    -   `feature/xxx`: For new features (e.g., `feature/quoting`).
    -   `fix/xxx`: For bug fixes (e.g., `fix/pricing-api-bug`).
2.  **Branch Operations**:
    -   Always branch from `develop`.
    -   Submit Pull Requests (PRs) to `develop` for code review.
    -   Merge to `main` only for releases.
3.  **Commits**:
    -   Follow Conventional Commits (`feat(quoting): add seller online quoting`).
    -   Run `lint` and `type-check` locally before committing.

### 10.3. Testing
1.  **Local Dev Testing**: Backend APIs should have unit tests (`go test`). Frontend components should have basic interaction tests.
2.  **Integration Testing**: CI is triggered on merges to `develop`, running linting, unit tests, and builds.
3.  **Acceptance Testing**: QA validates features in a staging environment based on PRD acceptance criteria.

### 10.4. Deployment
1.  **Automated Deployment**: Use GitHub Actions for CI/CD.
2.  **Rollback & Monitoring**: Tag releases for easy rollback. Monitor production environment for errors and performance issues.

### 10.5. Workflow Diagram
```mermaid
graph TD
A[Requirement Review] --> B[Task Breakdown]
B --> C[feature Branch Dev]
C --> D[Local Testing]
D --> E[PR to develop]
E --> F[CI Automated Tests]
F --> G[Staging/QA]
G --> H[Release to main]
H --> I[Production Deploy]
I --> J[Monitor & Iterate]
```

## Development Memories

- This document has been updated by merging the contents of `docs/技术栈与开发规范.md`. It now contains comprehensive guidelines on tech stack, development standards, code structure, and workflows.

## 11. 数据结构一致性 (Data Structure Consistency)

### 11.1. 核心原则

为保证项目在多端（`admin/server`, `admin/web`, `app`, Python下单端）数据交互的准确性，所有核心业务实体的数据结构必须保持一致。我们遵循以下原则：

1.  **后端为王 (Backend as Golden Source)**: 后端 `admin/server` 项目中的 `model` 定义是数据结构的“黄金标准”和唯一事实来源。所有前端和客户端项目的数据结构都必须与后端对齐。
2.  **显式定义 (Explicit Definition)**: 所有跨项目共享的核心数据结构，都必须在前端项目中进行显式的 `TypeScript` 类型定义，或在 Python 项目中进行 `Pydantic` 模型定义。禁止使用 `any` 或隐式推断。
3.  **集中管理 (Centralized Mapping)**: 本章节的映射表是定位这些数据结构定义的唯一入口。**任何新增或修改核心业务模型的操作，都必须同步更新此处的映射表。**

### 11.2. 核心业务实体映射表

下表列出了项目中核心业务实体在不同项目中的文件位置。

| 业务概念 (Concept) | 后端 (admin/server) - Go Struct | 用户端 (app) - TS Interface | 后台 (admin/web) - TS Interface | 下单端 (Python) - Pydantic Model |
| :--- | :--- | :--- | :--- | :--- |
| **用户 (User)** | `server/model/system/sys_user.go` | `src/types/user.ts` | `src/api/user.ts` | `futures_trader/models/user.py` |
| **角色 (Role)** | `server/model/system/sys_authority.go` | `src/types/role.ts` | `src/api/role.ts` | `futures_trader/models/role.py` |
| **合约 (Contract)** | `server/model/dianjia/contract.go` | `src/types/contract.ts` | `src/api/contract.ts` | `futures_trader/models/contract.py` |
| **订单 (Order)** | `server/model/dianjia/order.go` | `src/types/order.ts` | `src/api/order.ts` | `futures_trader/models/order.py` |

**使用说明:**
- 当你需要了解一个业务实体（如“合约”）在前端、后端或客户端的具体字段时，请通过此表快速定位到其定义文件。
- 在进行功能开发时，后端应首先在 `server/model/dianjia/` 目录下定义好 `Go struct`，然后各端依据此结构在各自项目中创建对应的 `TypeScript interface` 或 `Pydantic model`。

---

## Vue 泛型 defineProps 使用陷阱与最佳实践

### 1. defineProps 泛型的编译陷阱
不要使用
```ts
const props = withDefaults(defineProps<IInstrumentSelectorProps>(), {
  placeholder: '请选择期货合约',
  disabled: false,
  clearable: true
})
const emit = defineEmits<IInstrumentSelectorEmits>()
```
而是使用
```ts
const props = defineProps({
  placeholder: '请选择期货合约',
  disabled: false,
  clearable: true
}) as IInstrumentSelectorProps

const emit = defineEmits() as IInstrumentSelectorEmits
```


## 组件开发规范
1. 不需要在组件中使用 `route` 块，因为组件是独立的，不需要路由管理。
2. 自定义选择性的组件统一返回 unit 类型，不要返回 any 类型。而且watch 输入的类型，输入unit后，自动查找显示的文本。

