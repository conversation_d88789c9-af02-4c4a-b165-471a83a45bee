# 个人资料与密码管理功能

根据 `docs/features/14-user-account-settings/02_Profile_and_Password_Management.md` 方案实现的用户账户设置功能。

## 功能概述

### 1. 账户中心主页 (`index.vue`)

**功能特性：**
- 用户头像展示与编辑（点击头像可选择图片）
- 个人资料展示与编辑（昵称、企业名称）
- 账户信息展示（手机号、用户ID）
- 账户安全入口（修改密码）
- 退出登录功能

**主要组件：**
- 用户信息卡片：展示头像、昵称、用户ID
- 个人资料列表：昵称、手机号、企业名称
- 账户安全列表：修改密码入口
- 编辑弹窗：支持昵称和企业名称的在线编辑

### 2. 修改密码页面 (`change-password.vue`)

**功能特性：**
- 安全的密码修改流程
- 完整的表单验证（旧密码、新密码、确认密码）
- 密码强度提示
- 修改成功后强制重新登录

**安全特性：**
- 旧密码验证
- 新密码与确认密码一致性检查
- 新旧密码不能相同
- 密码长度验证（最少6位）
- 修改成功后清除本地凭证

## 技术实现

### API接口复用

按照方案要求，完全复用现有后端接口：

1. **获取用户信息**: `GET /user/getProfile`
2. **更新用户信息**: `PUT /user/updateProfile`
3. **修改密码**: `POST /user/changePassword`
4. **文件上传**: `POST /fileUploadAndDownload/upload` (头像上传，待实现)

### 前端技术栈

- **框架**: Vue 3 + TypeScript + uni-app
- **UI组件**: wot-design-uni
- **状态管理**: Pinia
- **样式**: SCSS + UnoCSS
- **路由**: 约定式路由

### 设计系统

遵循项目设计系统规范：
- **主色调**: #667eea 到 #764ba2 的渐变
- **卡片设计**: 20rpx圆角，毛玻璃效果，精致阴影
- **按钮样式**: 渐变背景，44rpx圆角，88rpx高度
- **动画效果**: 0.3s ease过渡动画

## 页面路由

```
/pages/profile/index           # 账户中心主页
/pages/profile/change-password # 修改密码页面
```

## 使用方式

### 1. 访问账户中心

```javascript
uni.navigateTo({
  url: '/pages/profile/index'
})
```

### 2. 修改密码

```javascript
uni.navigateTo({
  url: '/pages/profile/change-password'
})
```

### 3. 编辑个人资料

在账户中心页面点击对应字段即可进入编辑模式。

## 待完善功能

1. **头像上传与裁剪**
   - 集成图片裁剪组件
   - 实现文件上传功能
   - 支持多种图片格式

2. **更多个人信息字段**
   - 企业地址编辑
   - 邮箱绑定
   - 其他扩展字段

3. **安全增强**
   - 密码强度指示器
   - 登录设备管理
   - 操作日志查看

## 测试建议

1. **功能测试**
   - 测试个人资料编辑功能
   - 测试密码修改流程
   - 测试表单验证逻辑

2. **兼容性测试**
   - H5端功能测试
   - 小程序端功能测试
   - App端功能测试

3. **安全测试**
   - 密码修改安全性验证
   - 登录状态管理测试
   - 错误处理测试

## 注意事项

1. **安全性**
   - 密码修改后会强制重新登录
   - 所有敏感操作都需要验证用户身份

2. **用户体验**
   - 提供清晰的操作反馈
   - 支持取消操作
   - 错误信息友好提示

3. **数据一致性**
   - 更新资料后自动刷新用户信息
   - 保持前端状态与后端数据同步
