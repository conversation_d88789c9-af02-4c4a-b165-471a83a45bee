# 用户资料更新接口修复说明

## 问题描述

原有的 `UpdateUserProfile` 方法存在一个问题：当前端只更新一个字段时，其他字段会被设置为空值。

### 原始代码问题

```go
// 原始代码 - 有问题的实现
func (authService *AuthService) UpdateUserProfile(userUUID uuid.UUID, updateData system.SysUser) error {
    updates := map[string]interface{}{
        "nick_name":       updateData.NickName,        // 即使为空也会更新
        "header_img":      updateData.HeaderImg,       // 即使为空也会更新
        "company_name":    updateData.CompanyName,     // 即使为空也会更新
        "company_org_id":  updateData.CompanyOrgId,    // 即使为空也会更新
        "company_address": updateData.CompanyAddress,  // 即使为空也会更新
    }
    
    err := global.GVA_DB.Model(&system.SysUser{}).Where("uuid = ?", userUUID).Updates(updates).Error
    // ...
}
```

**问题场景：**
- 前端只想更新昵称：`{"nickName": "新昵称"}`
- 后端会执行：`UPDATE users SET nick_name='新昵称', header_img='', company_name='', company_org_id='', company_address='' WHERE uuid=?`
- 结果：其他字段被清空

## 修复方案

### 修复后的代码

```go
// 修复后的代码 - 只更新非空字段
func (authService *AuthService) UpdateUserProfile(userUUID uuid.UUID, updateData system.SysUser) error {
    // 只更新非空字段，避免清空其他字段
    updates := make(map[string]interface{})
    
    if updateData.NickName != "" {
        updates["nick_name"] = updateData.NickName
    }
    if updateData.HeaderImg != "" {
        updates["header_img"] = updateData.HeaderImg
    }
    if updateData.CompanyName != "" {
        updates["company_name"] = updateData.CompanyName
    }
    if updateData.CompanyOrgId != "" {
        updates["company_org_id"] = updateData.CompanyOrgId
    }
    if updateData.CompanyAddress != "" {
        updates["company_address"] = updateData.CompanyAddress
    }
    
    // 如果没有任何字段需要更新，直接返回成功
    if len(updates) == 0 {
        return nil
    }
    
    err := global.GVA_DB.Model(&system.SysUser{}).Where("uuid = ?", userUUID).Updates(updates).Error
    // ...
}
```

### 修复效果

**现在的行为：**
- 前端只想更新昵称：`{"nickName": "新昵称"}`
- 后端会执行：`UPDATE users SET nick_name='新昵称' WHERE uuid=?`
- 结果：只有昵称被更新，其他字段保持不变

## 测试用例

### 测试场景 1：只更新昵称
```json
// 请求
PUT /user/updateProfile
{
    "nickName": "新昵称"
}

// 期望结果：只有 nickName 字段被更新，其他字段保持原值
```

### 测试场景 2：只更新头像
```json
// 请求
PUT /user/updateProfile
{
    "headerImg": "https://example.com/new-avatar.jpg"
}

// 期望结果：只有 headerImg 字段被更新，其他字段保持原值
```

### 测试场景 3：更新多个字段
```json
// 请求
PUT /user/updateProfile
{
    "nickName": "新昵称",
    "companyName": "新公司"
}

// 期望结果：只有 nickName 和 companyName 字段被更新，其他字段保持原值
```

### 测试场景 4：空请求
```json
// 请求
PUT /user/updateProfile
{}

// 期望结果：没有字段被更新，直接返回成功
```

## 兼容性说明

- ✅ **向后兼容**：现有的前端代码无需修改
- ✅ **API 接口不变**：请求和响应格式保持一致
- ✅ **数据安全**：不会意外清空用户数据
- ✅ **性能优化**：空请求时不执行数据库操作

## 相关文件

- **修改文件**：`admin/server/service/dianjia/auth.go`
- **相关接口**：`PUT /user/updateProfile`
- **前端调用**：`app/src/store/user.ts` 中的 `updateUserProfile` 方法

## 注意事项

1. **字段清空需求**：如果将来需要支持主动清空某个字段，可以考虑：
   - 使用特殊值（如 `"__CLEAR__"`）表示清空
   - 添加额外的 `clearFields` 参数
   - 使用 PATCH 语义的专门接口

2. **数据验证**：建议在前端和后端都添加必要的数据验证

3. **日志记录**：可以考虑添加更详细的操作日志，记录具体更新了哪些字段
