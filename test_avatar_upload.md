# 头像上传功能测试指南

## 功能概述

头像上传功能采用两步流程：
1. **文件上传**：将本地图片上传到服务器，获取OSS地址
2. **资料更新**：使用获取到的OSS地址更新用户头像

## 技术实现

### 后端接口

**文件上传接口：**
- **URL**: `POST /fileUploadAndDownload/upload`
- **认证**: 需要在header中传递 `x-token` 或 `Authorization: Bearer <token>`
- **参数**: 
  - `file`: 图片文件（multipart/form-data）
  - `classId`: 分类ID（可选，默认为0）

**响应格式：**
```json
{
  "code": 0,
  "msg": "上传成功",
  "data": {
    "file": {
      "ID": 1,
      "CreatedAt": "2023-12-01T10:00:00Z",
      "UpdatedAt": "2023-12-01T10:00:00Z",
      "name": "avatar.jpg",
      "classId": 0,
      "url": "https://bucket.oss-region.aliyuncs.com/uploads/2023-12-01/avatar.jpg",
      "tag": "jpg",
      "key": "uploads/2023-12-01/avatar.jpg"
    }
  }
}
```

**用户资料更新接口：**
- **URL**: `PUT /user/updateProfile`
- **参数**: `{"headerImg": "https://oss-url..."}`

### 前端实现

**核心流程：**
```typescript
// 1. 用户选择图片
uni.chooseImage({
  count: 1,
  sizeType: ['original', 'compressed'],
  sourceType: ['album', 'camera'],
  success: (res) => {
    selectedImagePath.value = res.tempFilePaths[0]
    showAvatarCropper.value = true
  }
})

// 2. 上传文件获取OSS地址
uni.uploadFile({
  url: '/fileUploadAndDownload/upload',
  filePath: selectedImagePath.value,
  name: 'file',
  header: {
    'x-token': userStore.token,
    'Authorization': `Bearer ${userStore.token}`
  },
  formData: { classId: '0' },
  success: (res) => {
    const data = JSON.parse(res.data)
    const fileUrl = data.data.file.url
    // 3. 更新用户头像
    userStore.updateUserProfile({ headerImg: fileUrl })
  }
})
```

## 测试步骤

### 前置条件

1. **用户已登录**：确保有有效的认证token
2. **网络连接**：确保设备可以访问服务器
3. **存储权限**：确保应用有相册和相机访问权限

### 测试用例

#### 测试用例 1：从相册选择图片

**步骤：**
1. 打开账户中心页面
2. 点击用户头像
3. 在弹出的选择器中选择"从相册选择"
4. 选择一张图片
5. 在预览弹窗中点击"确认上传"

**预期结果：**
- 显示上传进度
- 上传成功后头像立即更新
- 显示"头像更新成功"提示
- 页面刷新后头像保持更新状态

#### 测试用例 2：使用相机拍照

**步骤：**
1. 打开账户中心页面
2. 点击用户头像
3. 在弹出的选择器中选择"拍照"
4. 拍摄一张照片
5. 在预览弹窗中点击"确认上传"

**预期结果：**
- 显示上传进度
- 上传成功后头像立即更新
- 显示"头像更新成功"提示

#### 测试用例 3：取消上传

**步骤：**
1. 选择图片后在预览弹窗中点击"取消"

**预期结果：**
- 弹窗关闭
- 头像不发生变化
- 没有网络请求发出

#### 测试用例 4：网络错误处理

**步骤：**
1. 断开网络连接
2. 尝试上传头像

**预期结果：**
- 显示网络错误提示
- 上传状态正确重置
- 用户可以重新尝试

#### 测试用例 5：大文件处理

**步骤：**
1. 选择一个超大的图片文件（>10MB）
2. 尝试上传

**预期结果：**
- 根据后端配置，可能会压缩或拒绝上传
- 显示适当的错误提示

## 调试信息

### 控制台日志

上传过程中会输出详细的调试信息：

```
开始上传头像，文件路径: /tmp/xxx.jpg
上传URL: https://api.example.com/fileUploadAndDownload/upload
用户Token: eyJhbGciOiJIUzI1NiIs...
文件上传响应: {statusCode: 200, data: "..."}
解析后的响应数据: {code: 0, msg: "上传成功", data: {...}}
上传成功，文件信息: {ID: 1, url: "https://...", ...}
文件上传成功，开始更新用户资料
获取到的文件URL: https://bucket.oss-region.aliyuncs.com/...
头像更新流程完成
```

### 常见错误

**1. 认证失败**
```
错误信息: "token验证失败"
解决方案: 检查用户是否已登录，token是否有效
```

**2. 文件格式不支持**
```
错误信息: "不支持的文件格式"
解决方案: 确保选择的是图片文件（jpg, png, gif等）
```

**3. 文件过大**
```
错误信息: "文件大小超出限制"
解决方案: 选择较小的图片或启用压缩
```

**4. 网络超时**
```
错误信息: "上传请求失败: 网络错误"
解决方案: 检查网络连接，重新尝试
```

## 性能优化

### 图片压缩

可以在上传前对图片进行压缩：

```typescript
uni.chooseImage({
  count: 1,
  sizeType: ['compressed'], // 只选择压缩版本
  sourceType: ['album', 'camera']
})
```

### 上传进度

可以监听上传进度：

```typescript
const uploadTask = uni.uploadFile({...})
uploadTask.onProgressUpdate((res) => {
  console.log('上传进度：', res.progress + '%')
  // 更新UI进度条
})
```

## 安全考虑

1. **文件类型验证**：后端会验证文件类型
2. **文件大小限制**：防止恶意上传大文件
3. **认证检查**：所有请求都需要有效token
4. **OSS安全**：使用安全的云存储配置

## 故障排除

### 问题：头像上传后不显示

**可能原因：**
1. OSS地址无法访问
2. 图片格式不被浏览器支持
3. 缓存问题

**解决方案：**
1. 检查OSS配置和权限
2. 确认图片格式正确
3. 清除浏览器缓存或添加时间戳

### 问题：上传速度慢

**可能原因：**
1. 图片文件过大
2. 网络环境差
3. OSS地域选择不当

**解决方案：**
1. 启用图片压缩
2. 优化网络环境
3. 选择就近的OSS地域
