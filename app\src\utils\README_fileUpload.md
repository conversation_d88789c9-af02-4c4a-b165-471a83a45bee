# 文件上传工具使用指南

## 概述

`fileUpload.ts` 提供了一套完整的文件上传工具函数，专门用于处理图片上传到服务器并获取OSS地址的场景。

## 核心功能

### 1. FileUploadUtil 工具类

#### uploadFile() - 上传文件到服务器

```typescript
import { FileUploadUtil } from '@/utils/fileUpload'

// 基本用法
const fileInfo = await FileUploadUtil.uploadFile(filePath, token)
console.log('文件URL:', fileInfo.url)

// 带进度监听
const fileInfo = await FileUploadUtil.uploadFile(filePath, token, {
  classId: '1',
  onProgress: (progress) => {
    console.log('上传进度:', progress + '%')
  }
})
```

#### chooseImage() - 选择图片

```typescript
// 基本用法
const filePath = await FileUploadUtil.chooseImage()

// 自定义选项
const filePath = await FileUploadUtil.chooseImage({
  count: 1,
  sizeType: ['compressed'], // 只选择压缩版本
  sourceType: ['album']     // 只从相册选择
})
```

#### validateImage() - 验证图片

```typescript
// 验证图片大小（默认10MB限制）
const isValid = await FileUploadUtil.validateImage(filePath)

// 自定义大小限制（5MB）
const isValid = await FileUploadUtil.validateImage(filePath, 5 * 1024 * 1024)
```

### 2. uploadAvatar() - 头像上传专用函数

```typescript
import { uploadAvatar } from '@/utils/fileUpload'

try {
  const avatarUrl = await uploadAvatar(userToken, (progress) => {
    console.log('上传进度:', progress + '%')
  })
  
  // 更新用户头像
  await updateUserProfile({ headerImg: avatarUrl })
} catch (error) {
  console.error('头像上传失败:', error)
}
```

## 在组件中使用

### 完整的头像上传流程

```vue
<template>
  <view class="avatar-upload">
    <image :src="userAvatar" @click="handleAvatarClick" />
    <view v-if="uploading" class="loading">上传中...</view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { FileUploadUtil } from '@/utils/fileUpload'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const userAvatar = ref(userStore.userInfo.headerImg)
const uploading = ref(false)

const handleAvatarClick = async () => {
  uploading.value = true
  try {
    // 1. 选择图片
    const filePath = await FileUploadUtil.chooseImage({
      sizeType: ['compressed']
    })
    
    // 2. 验证图片
    const isValid = await FileUploadUtil.validateImage(filePath)
    if (!isValid) return
    
    // 3. 上传文件
    const fileInfo = await FileUploadUtil.uploadFile(
      filePath, 
      userStore.token,
      {
        onProgress: (progress) => {
          console.log('上传进度:', progress + '%')
        }
      }
    )
    
    // 4. 更新用户头像
    await userStore.updateUserProfile({
      headerImg: fileInfo.url
    })
    
    userAvatar.value = fileInfo.url
    uni.showToast({ title: '头像更新成功' })
    
  } catch (error) {
    console.error('头像上传失败:', error)
    uni.showToast({ 
      title: error.message || '上传失败', 
      icon: 'none' 
    })
  } finally {
    uploading.value = false
  }
}
</script>
```

## API 参考

### FileUploadUtil.uploadFile()

**参数：**
- `filePath: string` - 本地文件路径
- `token: string` - 认证token
- `options?: object` - 上传选项
  - `classId?: string | number` - 文件分类ID（默认'0'）
  - `onProgress?: (progress: number) => void` - 进度回调

**返回：**
```typescript
Promise<{
  ID: number
  CreatedAt: string
  UpdatedAt: string
  name: string
  classId: number
  url: string      // 重要：OSS文件地址
  tag: string
  key: string
}>
```

### FileUploadUtil.chooseImage()

**参数：**
- `options?: object` - 选择选项
  - `count?: number` - 最多选择数量（默认1）
  - `sizeType?: ('original' | 'compressed')[]` - 图片尺寸类型
  - `sourceType?: ('album' | 'camera')[]` - 图片来源

**返回：**
```typescript
Promise<string> // 本地文件路径
```

### FileUploadUtil.validateImage()

**参数：**
- `filePath: string` - 文件路径
- `maxSize?: number` - 最大文件大小（字节，默认10MB）

**返回：**
```typescript
Promise<boolean> // 是否验证通过
```

### uploadAvatar()

**参数：**
- `token: string` - 用户认证token
- `onProgress?: (progress: number) => void` - 进度回调

**返回：**
```typescript
Promise<string> // OSS文件地址
```

## 错误处理

### 常见错误类型

1. **选择图片失败**
   ```typescript
   try {
     const filePath = await FileUploadUtil.chooseImage()
   } catch (error) {
     // error.message: "选择图片失败" 或 "未选择任何图片"
   }
   ```

2. **文件验证失败**
   ```typescript
   const isValid = await FileUploadUtil.validateImage(filePath)
   if (!isValid) {
     // 文件过大或其他验证失败
   }
   ```

3. **上传失败**
   ```typescript
   try {
     const fileInfo = await FileUploadUtil.uploadFile(filePath, token)
   } catch (error) {
     // error.message: "文件上传失败" 或 "网络错误" 等
   }
   ```

### 错误处理最佳实践

```typescript
const handleUpload = async () => {
  try {
    const filePath = await FileUploadUtil.chooseImage()
    const fileInfo = await FileUploadUtil.uploadFile(filePath, token)
    // 处理成功
  } catch (error) {
    if (error.message.includes('选择')) {
      // 用户取消选择，通常不需要显示错误
      return
    }
    
    // 显示具体错误信息
    uni.showToast({
      title: error.message || '操作失败',
      icon: 'none'
    })
  }
}
```

## 配置说明

### 环境变量

确保在 `.env` 文件中配置了正确的服务器地址：

```env
VITE_SERVER_BASEURL=https://your-api-server.com
```

### 后端接口

工具函数依赖以下后端接口：

- **上传接口**: `POST /fileUploadAndDownload/upload`
- **认证方式**: Header中的 `x-token` 或 `Authorization: Bearer <token>`
- **文件字段**: `file`
- **额外参数**: `classId`

## 性能优化建议

1. **使用压缩图片**：优先选择 `sizeType: ['compressed']`
2. **文件大小限制**：根据需要调整 `validateImage` 的大小限制
3. **进度反馈**：使用 `onProgress` 回调提供用户反馈
4. **错误重试**：在网络错误时提供重试机制

## 注意事项

1. **权限检查**：确保应用有相册和相机访问权限
2. **网络状态**：在弱网环境下考虑超时和重试
3. **文件格式**：后端会验证文件类型，确保选择正确的图片格式
4. **安全性**：所有上传都需要有效的认证token
