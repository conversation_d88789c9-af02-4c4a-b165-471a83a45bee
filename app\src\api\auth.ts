import { http } from '@/http/http'
import type {
  ISendCodeRequest,
  IPhoneLoginRequest,
  IUsernameLoginRequest,
  ICaptchaResponse,
} from '@/types'
import type { IUser, ILoginResponse, IUpdateProfileRequest } from '@/types'

// 为了保持兼容性，重新导出类型
export type {
  ISendCodeRequest,
  IPhoneLoginRequest,
  IUsernameLoginRequest,
  IUpdateProfileRequest,
  ICaptchaResponse,
}

/**
 * 发送登录验证码
 * @param data 包含手机号的请求数据
 */
export function sendLoginCode(data: ISendCodeRequest) {
  return http.post<{ code: number, msg: string }>('/user/sendLoginCode', data)
}

/**
 * 手机号验证码登录
 * @param data 包含手机号和验证码的登录数据
 */
export function loginByPhone(data: IPhoneLoginRequest) {
  return http.post<ILoginResponse>('/user/loginByPhone', data)
}

/**
 * 获取当前用户信息
 */
export function getProfile() {
  return http.get<{ userInfo: IUser }>('/user/getProfile')
}

/**
 * 更新用户资料
 * @param data 要更新的用户资料数据
 */
export function updateProfile(data: IUpdateProfileRequest) {
  return http.put<{ code: number, msg: string }>('/user/updateProfile', data)
}

/**
 * 获取微信登录凭证
 * @returns Promise 包含微信登录凭证(code)
 */
export function getWxCode() {
  return new Promise<UniApp.LoginRes>((resolve, reject) => {
    uni.login({
      provider: 'weixin',
      success: res => resolve(res),
      fail: err => reject(new Error(`微信登录失败: ${JSON.stringify(err)}`)),
    })
  })
}

/**
 * 微信登录
 * @param data 微信登录参数，包含code
 * @returns Promise 包含登录结果
 */
export function loginByWechat(data: { code: string }) {
  return http.post<ILoginResponse>('/user/loginByWechat', data)
}

/**
 * 修改密码
 * @param data 修改密码参数
 * @returns Promise 包含修改结果
 */
export function changePassword(data: { oldPassword: string; newPassword: string }) {
  return http.post<{ code: number; msg: string }>('/user/changePassword', data)
}

/**
 * 用户名密码登录
 * @param data 包含用户名、密码和验证码的登录数据
 */
export function loginByUsername(data: IUsernameLoginRequest) {
  return http.post<ILoginResponse>('/user/login', data)
}

/**
 * 获取图形验证码
 * @returns Promise 包含验证码信息
 */
export function getCaptcha() {
  return http.post<ICaptchaResponse>('/base/captcha')
}
