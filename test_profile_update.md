# 用户资料更新功能测试指南

## 测试目的

验证修复后的 `updateProfile` 接口只更新传递的非空字段，不会清空其他字段。

## 测试前准备

1. 确保后端服务正在运行
2. 确保有一个测试用户账号
3. 记录用户当前的完整资料信息

## 测试步骤

### 步骤 1：获取用户当前信息

```bash
# 使用 curl 或 Postman 获取用户信息
GET /user/getProfile
Authorization: Bearer <your_token>
```

**记录当前用户信息：**
```json
{
  "nickName": "原昵称",
  "headerImg": "原头像URL",
  "companyName": "原公司名",
  "companyOrgId": "原组织ID",
  "companyAddress": "原公司地址"
}
```

### 步骤 2：测试只更新昵称

```bash
PUT /user/updateProfile
Authorization: Bearer <your_token>
Content-Type: application/json

{
  "nickName": "测试新昵称"
}
```

**验证结果：**
- 再次调用 `GET /user/getProfile`
- 确认只有 `nickName` 被更新为 "测试新昵称"
- 确认其他字段（`headerImg`, `companyName`, `companyOrgId`, `companyAddress`）保持原值

### 步骤 3：测试只更新公司名

```bash
PUT /user/updateProfile
Authorization: Bearer <your_token>
Content-Type: application/json

{
  "companyName": "测试新公司"
}
```

**验证结果：**
- 再次调用 `GET /user/getProfile`
- 确认只有 `companyName` 被更新为 "测试新公司"
- 确认 `nickName` 仍为 "测试新昵称"（上一步的值）
- 确认其他字段保持原值

### 步骤 4：测试更新多个字段

```bash
PUT /user/updateProfile
Authorization: Bearer <your_token>
Content-Type: application/json

{
  "nickName": "最终昵称",
  "companyAddress": "新公司地址"
}
```

**验证结果：**
- 再次调用 `GET /user/getProfile`
- 确认 `nickName` 被更新为 "最终昵称"
- 确认 `companyAddress` 被更新为 "新公司地址"
- 确认 `companyName` 仍为 "测试新公司"（上一步的值）
- 确认其他字段保持原值

### 步骤 5：测试空请求

```bash
PUT /user/updateProfile
Authorization: Bearer <your_token>
Content-Type: application/json

{}
```

**验证结果：**
- 接口应该返回成功
- 再次调用 `GET /user/getProfile`
- 确认所有字段都没有变化

## 前端测试

### 在账户中心页面测试

1. **打开账户中心页面**
   ```javascript
   uni.navigateTo({
     url: '/pages/profile/index'
   })
   ```

2. **测试昵称编辑**
   - 点击昵称行
   - 修改昵称为 "前端测试昵称"
   - 点击保存
   - 验证页面显示更新，其他信息不变

3. **测试企业名称编辑**
   - 点击企业名称行
   - 修改企业名称为 "前端测试公司"
   - 点击保存
   - 验证页面显示更新，昵称保持上一步的值

4. **测试头像上传**
   - 点击头像
   - 选择新图片
   - 确认上传
   - 验证头像更新，其他信息不变

## 预期结果

✅ **成功标准：**
- 每次只更新传递的字段
- 未传递的字段保持原有值不变
- 前端界面正确显示更新后的信息
- 没有出现字段被意外清空的情况

❌ **失败标准：**
- 更新一个字段时，其他字段被清空
- 接口返回错误
- 前端显示异常

## 回滚测试

如果测试失败，可以手动恢复用户信息：

```bash
PUT /user/updateProfile
Authorization: Bearer <your_token>
Content-Type: application/json

{
  "nickName": "原昵称",
  "headerImg": "原头像URL", 
  "companyName": "原公司名",
  "companyOrgId": "原组织ID",
  "companyAddress": "原公司地址"
}
```

## 注意事项

1. **测试环境**：建议在开发或测试环境进行，避免影响生产数据
2. **数据备份**：测试前备份用户数据
3. **权限验证**：确保使用正确的认证 token
4. **错误处理**：注意观察错误日志和响应信息
